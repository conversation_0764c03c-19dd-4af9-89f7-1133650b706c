import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: BorderRadius.RADIUS_08,
    borderColor: ColorsV2.neutral100,
    marginTop: Spacing.SPACE_16,
  },
  textValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContainer: {
    width: '48.5%',
    borderWidth: 1,
    marginTop: Spacing.SPACE_08,
    padding: Spacing.SPACE_08,
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: ColorsV2.neutral50,
    borderColor: ColorsV2.neutral100,
  },
  contentContainer: {
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
    marginRight: Spacing.SPACE_08,
    marginLeft: Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_08,
    paddingTop: Spacing.SPACE_08,
  },
  headerContainer: {
    padding: Spacing.SPACE_08,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
