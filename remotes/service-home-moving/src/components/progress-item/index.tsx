import React, { useEffect, useMemo, useRef } from 'react';
import { Animated, ImageProps } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  ConfigHelpers,
  HitSlop,
  IconImage,
  TouchableOpacity,
} from '@btaskee/design-system';

import { styles } from './styles';

type ProgressItemProps = {
  isActive?: boolean;
  isPass?: boolean;
  isHideLine?: boolean;
  sourceIcon: ImageProps['source'];
  size: number;
  onPress?: () => void;
};

export const ProgressItem = ({ 
  isActive, 
  isPass, 
  isHideLine, 
  sourceIcon, 
  size = 32, 
  onPress 
}: ProgressItemProps) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const lineOpacity = useRef(new Animated.Value(1)).current;

  const data = useMemo(() => {
    let background = ColorsV2.orange50;
    let colorIcon = ColorsV2.orange500;
    let sizeIcon = 18;

    if (isActive) {
      background = ColorsV2.orange500;
      colorIcon = ColorsV2.neutralWhite;
      sizeIcon = 18;
    } else if (isPass) {
      background = ColorsV2.green50;
      colorIcon = ColorsV2.green500;
      sizeIcon = 18;
    }
    
    return {
      background,
      colorIcon,
      sizeIcon,
    };
  }, [isActive, isPass]);

  useEffect(() => {
    const duration = ConfigHelpers.isE2ETesting ? 0 : 500;
    
    Animated.timing(lineOpacity, {
      toValue: isPass ? 0 : 1,
      duration: duration,
      useNativeDriver: true,
    }).start();

    if (isActive && !ConfigHelpers.isE2ETesting) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    }
  }, [scaleAnim, isActive, isPass, lineOpacity]);

  return (
    <BlockView row center flex={isHideLine ? 0 : 1}>
      <ConditionView
        condition={!isHideLine}
        viewTrue={
          <BlockView style={styles.lineContainer}>
            <Animated.View
              style={[
                styles.line,
                {
                  opacity: lineOpacity,
                },
              ]}
            />
          </BlockView>
        }
      />
      <TouchableOpacity
        activeOpacity={0.9}
        hitSlop={HitSlop.MEDIUM}
        style={[
          styles.iconContainer,
          {
            width: size,
            height: size,
            borderRadius: size,
          },
        ]}
        onPress={onPress}
        disabled={!isPass}
      >
        <IconImage 
          source={sourceIcon} 
          size={data.sizeIcon} 
          color={data.colorIcon} 
        />
        <Animated.View
          style={[
            styles.animatedView,
            {
              transform: [{ scale: scaleAnim }],
              backgroundColor: data.background,
              borderRadius: size,
            },
          ]}
        />
      </TouchableOpacity>
    </BlockView>
  );
};
