import { StyleSheet } from 'react-native';
import { ColorsV2 } from '@btaskee/design-system';

const HEIGHT_LINE = 1;

export const styles = StyleSheet.create({
  lineContainer: {
    flex: 1,
    height: '100%',
    overflow: 'hidden',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedView: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
    zIndex: -1,
  },
  line: {
    position: 'absolute',
    left: -2,
    right: -2,
    top: -HEIGHT_LINE,
    bottom: `${50 - HEIGHT_LINE}%`,
    borderWidth: HEIGHT_LINE,
    zIndex: -1,
    borderStyle: 'dashed',
    borderRadius: 1,
    borderColor: ColorsV2.orange500,
  },
});
