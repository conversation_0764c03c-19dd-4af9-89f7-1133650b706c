import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  Icon,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface AddIsInBuildingButtonProps {
  onPress?: () => void;
}

export const AddIsInBuildingButton = ({ onPress }: AddIsInBuildingButtonProps) => {
  const { t } = useI18n();

  return (
    <TouchableOpacity
      testID="addIsInBuildingButton"
      activeOpacity={0.7}
      style={[
        styles.container,
        {
          borderColor: ColorsV2.orange500,
        },
      ]}
      onPress={onPress}
    >
      <Icon
        name="icLocation"
        size={Spacing.SPACE_20}
        color={ColorsV2.orange500}
        style={{ marginRight: Spacing.SPACE_08 }}
      />
      <CText
        color={ColorsV2.orange500}
        size={FontSizes.SIZE_16}
      >
        {t('TRANSPORTATION_IN_THE_SAME_BUILDING')}
      </CText>
    </TouchableOpacity>
  );
};
