import React from 'react';
import {
  <PERSON><PERSON>,
  BlockView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  PrimaryButton,
  SizedBox,
  Spacing,
  useI18n as useCommonI18n,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { imgChangeLocation } from '@images';

import { styles } from './styles';

type ConfirmChangeHomeTypeContentProps = {
  onConfirm?: () => void;
};

export const ConfirmChangeHomeTypeContent = ({ 
  onConfirm 
}: ConfirmChangeHomeTypeContentProps) => {
  const { t } = useI18n();
  const { t: tCommon } = useCommonI18n('common');

  const confirm = () => {
    close();
    onConfirm?.();
  };

  const close = () => {
    Alert.alert?.close?.();
  };

  return (
    <BlockView testID="confirmChangeHomeTypeView" style={styles.container}>
      <BlockView style={styles.content}>
        <BlockView 
          center 
          width={'100%'} 
          margin={{ bottom: Spacing.SPACE_24 }}
        >
          <FastImage 
            source={imgChangeLocation} 
            style={{ width: 240, height: 240 }}
            resizeMode="contain"
          />
        </BlockView>
        <CText 
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral800}
          style={{ textAlign: 'center' }}
        >
          {t('CONFIRM_CHANGE_DES')}
        </CText>
      </BlockView>
      <BlockView 
        row 
        padding={{ horizontal: Spacing.SPACE_16 }}
        style={{ marginTop: Spacing.SPACE_24 }}
      >
        <PrimaryButton
          title={tCommon('CLOSE')}
          backgroundColor={ColorsV2.neutral100}
          titleColor={ColorsV2.orange500}
          style={styles.button}
          onPress={close}
        />
        <SizedBox width={Spacing.SPACE_16} />
        <PrimaryButton
          testID="confirmChangeHomeTypeButton"
          title={tCommon('CONFIRM')}
          style={styles.button}
          onPress={confirm}
        />
      </BlockView>
    </BlockView>
  );
};
